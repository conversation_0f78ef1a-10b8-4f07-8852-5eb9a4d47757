#!/bin/bash

# Customer Services Startup Script
# This script starts both the customer backend (Golang) and frontend (Next.js)

set -e

echo "🚀 Starting Customer Services..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Check if required tools are installed
check_dependencies() {
    print_header "🔍 Checking dependencies..."
    
    if ! command -v go &> /dev/null; then
        print_error "Go is not installed. Please install Go 1.21 or higher."
        exit 1
    fi
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18 or higher."
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    print_status "All dependencies are installed ✅"
}

# Setup environment files
setup_environment() {
    print_header "🔧 Setting up environment..."
    
    # Customer backend environment
    if [ ! -f "customer-backend/.env" ]; then
        print_warning "Customer backend .env file not found. Creating from example..."
        cp customer-backend/.env.example customer-backend/.env
        print_status "Created customer-backend/.env from example"
        print_warning "Please update the database credentials in customer-backend/.env"
    fi
    
    # Customer frontend environment
    if [ ! -f ".env.local" ]; then
        print_warning "Frontend .env.local file not found. Creating from example..."
        cp .env.local.example .env.local
        print_status "Created .env.local from example"
    fi
}

# Install dependencies
install_dependencies() {
    print_header "📦 Installing dependencies..."
    
    # Install frontend dependencies
    print_status "Installing frontend dependencies..."
    npm install
    
    # Install backend dependencies
    print_status "Installing backend dependencies..."
    cd customer-backend
    go mod download
    cd ..
    
    print_status "Dependencies installed ✅"
}

# Build backend
build_backend() {
    print_header "🔨 Building customer backend..."
    cd customer-backend
    go build -o bin/customer-api cmd/server/main.go
    cd ..
    print_status "Backend built successfully ✅"
}

# Start services
start_services() {
    print_header "🚀 Starting services..."
    
    # Create log directory
    mkdir -p logs
    
    # Start backend in background
    print_status "Starting customer backend on port 8081..."
    cd customer-backend
    ./bin/customer-api > ../logs/backend.log 2>&1 &
    BACKEND_PID=$!
    cd ..
    
    # Wait a moment for backend to start
    sleep 3
    
    # Check if backend is running
    if kill -0 $BACKEND_PID 2>/dev/null; then
        print_status "Backend started successfully (PID: $BACKEND_PID) ✅"
    else
        print_error "Failed to start backend. Check logs/backend.log for details."
        exit 1
    fi
    
    # Start frontend
    print_status "Starting customer frontend on port 3000..."
    npm run dev > logs/frontend.log 2>&1 &
    FRONTEND_PID=$!
    
    # Wait a moment for frontend to start
    sleep 5
    
    # Check if frontend is running
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        print_status "Frontend started successfully (PID: $FRONTEND_PID) ✅"
    else
        print_error "Failed to start frontend. Check logs/frontend.log for details."
        kill $BACKEND_PID 2>/dev/null
        exit 1
    fi
    
    # Save PIDs for cleanup
    echo $BACKEND_PID > logs/backend.pid
    echo $FRONTEND_PID > logs/frontend.pid
    
    print_header "🎉 Customer services are running!"
    echo ""
    echo "📱 Frontend: http://localhost:3000"
    echo "🔧 Backend API: http://localhost:8081"
    echo "📊 Health Check: http://localhost:8081/health"
    echo ""
    echo "📝 Logs:"
    echo "   Backend: logs/backend.log"
    echo "   Frontend: logs/frontend.log"
    echo ""
    echo "🛑 To stop services, run: ./stop-customer-services.sh"
    echo ""
    
    # Keep script running and show logs
    print_status "Showing live logs (Ctrl+C to stop)..."
    tail -f logs/backend.log logs/frontend.log
}

# Cleanup function
cleanup() {
    print_header "🧹 Cleaning up..."
    
    if [ -f logs/backend.pid ]; then
        BACKEND_PID=$(cat logs/backend.pid)
        if kill -0 $BACKEND_PID 2>/dev/null; then
            kill $BACKEND_PID
            print_status "Stopped backend (PID: $BACKEND_PID)"
        fi
        rm logs/backend.pid
    fi
    
    if [ -f logs/frontend.pid ]; then
        FRONTEND_PID=$(cat logs/frontend.pid)
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            kill $FRONTEND_PID
            print_status "Stopped frontend (PID: $FRONTEND_PID)"
        fi
        rm logs/frontend.pid
    fi
    
    print_status "Cleanup completed ✅"
}

# Trap cleanup on script exit
trap cleanup EXIT INT TERM

# Main execution
main() {
    print_header "🏪 Customer Services Startup"
    echo ""
    
    check_dependencies
    setup_environment
    install_dependencies
    build_backend
    start_services
}

# Run main function
main "$@"
