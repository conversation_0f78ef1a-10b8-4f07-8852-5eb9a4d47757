package services

import (
	"context"
	"customer-backend/internal/repositories"
	"customer-backend/internal/types"
	"customer-backend/pkg/logger"
	"customer-backend/pkg/pagination"
	"fmt"

	"github.com/google/uuid"
)

// MenuService handles business logic for menu operations
type MenuService struct {
	menuRepo *repositories.MenuRepository
	shopRepo *repositories.ShopRepository
	logger   *logger.Logger
}

// NewMenuService creates a new menu service
func NewMenuService(menuRepo *repositories.MenuRepository, shopRepo *repositories.ShopRepository, logger *logger.Logger) *MenuService {
	return &MenuService{
		menuRepo: menuRepo,
		shopRepo: shopRepo,
		logger:   logger,
	}
}

// GetMenuItems retrieves menu items for a shop with pagination
func (s *MenuService) GetMenuItems(ctx context.Context, shopID uuid.UUID, filters types.MenuFilters) (*types.MenuListResponse, error) {
	s.logger.Infof("Getting menu items for shop ID: %s, page=%d, limit=%d", shopID.String(), filters.Page, filters.Limit)

	// Validate shop access
	if err := s.validateShopAccess(ctx, shopID); err != nil {
		return nil, err
	}

	// Apply defaults
	filters.CustomerPagination.ApplyDefaults()
	filters.CustomerSorting.ApplyDefaults()

	// Validate and adjust limits
	if filters.Limit > 100 {
		filters.Limit = 100
	}

	// Get menu items
	items, total, err := s.menuRepo.GetMenuItems(ctx, shopID, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get menu items")
		return nil, fmt.Errorf("failed to get menu items: %w", err)
	}

	// Get categories
	categories, err := s.menuRepo.GetMenuCategories(ctx, shopID)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to get menu categories")
		categories = []types.MenuCategory{} // Continue without categories
	}

	// Get filter options
	filterOptions := s.getMenuFilterOptions(ctx, shopID)

	// Create pagination info with center pagination
	paginationConfig := pagination.DefaultCenterConfig()
	paginationInfo := pagination.CreatePaginationInfo(total, filters.Page, filters.Limit, paginationConfig)

	// Enrich menu items with additional data
	s.enrichMenuItems(items)

	// Create response
	response := &types.MenuListResponse{}
	response.CustomerResponse = types.CreateCustomerResponse(
		struct {
			Items      []types.CustomerMenuItem `json:"items"`
			Categories []types.MenuCategory     `json:"categories"`
			Filters    types.MenuFilterOptions  `json:"filters"`
		}{
			Items:      items,
			Categories: categories,
			Filters:    filterOptions,
		},
		"Menu items retrieved successfully",
		paginationInfo,
	)

	return response, nil
}

// GetMenuItem retrieves a single menu item by ID
func (s *MenuService) GetMenuItem(ctx context.Context, itemID uuid.UUID) (*types.MenuItemResponse, error) {
	s.logger.Infof("Getting menu item ID: %s", itemID.String())

	item, err := s.menuRepo.GetMenuItem(ctx, itemID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get menu item")
		return nil, fmt.Errorf("failed to get menu item: %w", err)
	}

	// Enrich menu item with additional data
	s.enrichMenuItem(item)

	// Create response
	response := &types.MenuItemResponse{}
	response.CustomerResponse = types.CreateCustomerResponse(
		struct {
			Item types.CustomerMenuItem `json:"item"`
		}{Item: *item},
		"Menu item retrieved successfully",
		nil,
	)

	return response, nil
}

// GetPopularItems retrieves popular menu items for a shop
func (s *MenuService) GetPopularItems(ctx context.Context, shopID uuid.UUID, filters types.MenuFilters) (*types.MenuListResponse, error) {
	s.logger.Infof("Getting popular items for shop ID: %s", shopID.String())

	// Set filters for popular items
	isPopular := true
	filters.IsPopular = &isPopular
	filters.SortBy = "rating"
	filters.SortOrder = "desc"

	return s.GetMenuItems(ctx, shopID, filters)
}

// GetNewItems retrieves new menu items for a shop
func (s *MenuService) GetNewItems(ctx context.Context, shopID uuid.UUID, filters types.MenuFilters) (*types.MenuListResponse, error) {
	s.logger.Infof("Getting new items for shop ID: %s", shopID.String())

	// Set filters for new items
	isNew := true
	filters.IsNew = &isNew
	filters.SortBy = "created_at"
	filters.SortOrder = "desc"

	return s.GetMenuItems(ctx, shopID, filters)
}

// GetItemsByCategory retrieves menu items by category
func (s *MenuService) GetItemsByCategory(ctx context.Context, shopID uuid.UUID, categoryID uuid.UUID, filters types.MenuFilters) (*types.MenuListResponse, error) {
	s.logger.Infof("Getting items by category for shop ID: %s, category ID: %s", shopID.String(), categoryID.String())

	// Set category filter
	filters.CategoryID = &categoryID

	return s.GetMenuItems(ctx, shopID, filters)
}

// SearchMenuItems searches for menu items based on query
func (s *MenuService) SearchMenuItems(ctx context.Context, shopID uuid.UUID, query string, filters types.MenuFilters) (*types.MenuListResponse, error) {
	s.logger.Infof("Searching menu items for shop ID: %s, query: %s", shopID.String(), query)

	// Set search filter
	filters.Search = query

	// Default sorting for search results
	if filters.SortBy == "" {
		filters.SortBy = "rating"
	}

	return s.GetMenuItems(ctx, shopID, filters)
}

// GetVegetarianItems retrieves vegetarian menu items
func (s *MenuService) GetVegetarianItems(ctx context.Context, shopID uuid.UUID, filters types.MenuFilters) (*types.MenuListResponse, error) {
	s.logger.Infof("Getting vegetarian items for shop ID: %s", shopID.String())

	// Set vegetarian filter
	isVegetarian := true
	filters.IsVegetarian = &isVegetarian

	return s.GetMenuItems(ctx, shopID, filters)
}

// GetVeganItems retrieves vegan menu items
func (s *MenuService) GetVeganItems(ctx context.Context, shopID uuid.UUID, filters types.MenuFilters) (*types.MenuListResponse, error) {
	s.logger.Infof("Getting vegan items for shop ID: %s", shopID.String())

	// Set vegan filter
	isVegan := true
	filters.IsVegan = &isVegan

	return s.GetMenuItems(ctx, shopID, filters)
}

// GetGlutenFreeItems retrieves gluten-free menu items
func (s *MenuService) GetGlutenFreeItems(ctx context.Context, shopID uuid.UUID, filters types.MenuFilters) (*types.MenuListResponse, error) {
	s.logger.Infof("Getting gluten-free items for shop ID: %s", shopID.String())

	// Set gluten-free filter
	isGlutenFree := true
	filters.IsGlutenFree = &isGlutenFree

	return s.GetMenuItems(ctx, shopID, filters)
}

// GetMenuCategories retrieves menu categories for a shop
func (s *MenuService) GetMenuCategories(ctx context.Context, shopID uuid.UUID) ([]types.MenuCategory, error) {
	s.logger.Infof("Getting menu categories for shop ID: %s", shopID.String())

	// Validate shop access
	if err := s.validateShopAccess(ctx, shopID); err != nil {
		return nil, err
	}

	categories, err := s.menuRepo.GetMenuCategories(ctx, shopID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get menu categories")
		return nil, fmt.Errorf("failed to get menu categories: %w", err)
	}

	return categories, nil
}

// Helper methods

// validateShopAccess validates that the shop is accessible
func (s *MenuService) validateShopAccess(ctx context.Context, shopID uuid.UUID) error {
	shop, err := s.shopRepo.GetShopSettings(ctx, shopID)
	if err != nil {
		return fmt.Errorf("shop not found or not accessible: %w", err)
	}

	if !shop.IsActive {
		return fmt.Errorf("shop is not active")
	}

	return nil
}

// enrichMenuItems adds additional computed fields to menu items
func (s *MenuService) enrichMenuItems(items []types.CustomerMenuItem) {
	for i := range items {
		s.enrichMenuItem(&items[i])
	}
}

// enrichMenuItem adds additional computed fields to a menu item
func (s *MenuService) enrichMenuItem(item *types.CustomerMenuItem) {
	// Add computed fields or business logic transformations
	
	// Example: Calculate estimated preparation time based on complexity
	if item.PreparationTime == nil {
		estimatedTime := s.estimatePreparationTime(item)
		item.PreparationTime = &estimatedTime
	}
	
	// Example: Add promotional pricing
	// item.DiscountedPrice = s.calculateDiscountedPrice(item)
	
	// Example: Add availability based on time of day
	// item.IsAvailable = s.checkAvailabilityByTime(item)
}

// estimatePreparationTime estimates preparation time for items without it
func (s *MenuService) estimatePreparationTime(item *types.CustomerMenuItem) int {
	// Simple estimation based on category and complexity
	baseTime := 15 // minutes
	
	if item.IsSpicy && item.SpicyLevel > 3 {
		baseTime += 5
	}
	
	if item.HasCustomizations {
		baseTime += 3
	}
	
	return baseTime
}

// getMenuFilterOptions retrieves available filter options for menu items
func (s *MenuService) getMenuFilterOptions(ctx context.Context, shopID uuid.UUID) types.MenuFilterOptions {
	// This would typically query the database for available options
	// For now, return static options
	
	categories, _ := s.menuRepo.GetMenuCategories(ctx, shopID)
	
	return types.MenuFilterOptions{
		Categories: categories,
		PriceRanges: []types.PriceRange{
			{Label: "Under $10", Min: 0, Max: 10},
			{Label: "$10 - $20", Min: 10, Max: 20},
			{Label: "$20 - $30", Min: 20, Max: 30},
			{Label: "Over $30", Min: 30, Max: 1000},
		},
		DietaryOptions: []string{
			"vegetarian", "vegan", "gluten_free", "dairy_free", "nut_free",
		},
		SpicyLevels: []int{1, 2, 3, 4, 5},
		Allergens: []string{
			"nuts", "dairy", "eggs", "soy", "wheat", "shellfish", "fish",
		},
	}
}
