package routes

import (
	"customer-backend/internal/api/handlers"
	"customer-backend/internal/api/middleware"
	"customer-backend/internal/config"
	"customer-backend/internal/repositories"
	"customer-backend/internal/services"
	"customer-backend/pkg/logger"
	"net/http"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SetupRoutes configures all routes for the customer API
func SetupRoutes(router *gin.Engine, db *gorm.DB, cfg *config.Config, logger *logger.Logger) {
	// Initialize repositories
	shopRepo := repositories.NewShopRepository(db, logger)
	menuRepo := repositories.NewMenuRepository(db, logger)

	// Initialize services
	shopService := services.NewShopService(shopRepo, logger)
	menuService := services.NewMenuService(menuRepo, shopRepo, logger)

	// Initialize handlers
	shopHandler := handlers.NewShopHandler(shopService, logger)
	menuHandler := handlers.NewMenuHandler(menuService, logger)

	// Global middleware
	router.Use(middleware.CustomerCORSMiddleware())
	router.Use(middleware.RequestIDMiddleware())
	router.Use(middleware.ResponseTimeMiddleware())
	router.Use(middleware.LoggingMiddleware(logger))
	router.Use(gin.Recovery())

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "healthy",
			"service": "customer-api",
			"version": "1.0.0",
		})
	})

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Shop routes
		shops := v1.Group("/shops")
		{
			shops.GET("", shopHandler.GetShops)
			shops.GET("/search", shopHandler.SearchShops)
			shops.GET("/popular", shopHandler.GetPopularShops)
			shops.GET("/nearby", shopHandler.GetNearbyShops)
			shops.GET("/category/:category", shopHandler.GetShopsByCategory)
			shops.GET("/:id", shopHandler.GetShop)
			shops.GET("/:id/status", shopHandler.GetShopStatus)

			// Menu routes under shops
			shops.GET("/:shopId/menu", menuHandler.GetMenuItems)
			shops.GET("/:shopId/menu/popular", menuHandler.GetPopularItems)
			shops.GET("/:shopId/menu/new", menuHandler.GetNewItems)
			shops.GET("/:shopId/menu/vegetarian", menuHandler.GetVegetarianItems)
			shops.GET("/:shopId/menu/search", menuHandler.SearchMenuItems)
			shops.GET("/:shopId/menu/categories", menuHandler.GetMenuCategories)
			shops.GET("/:shopId/menu/categories/:categoryId", menuHandler.GetItemsByCategory)
		}

		// Direct menu item routes
		menu := v1.Group("/menu")
		{
			menu.GET("/items/:itemId", menuHandler.GetMenuItem)
		}

		// Additional customer-specific routes can be added here
		// For example: orders, reservations, reviews, etc.
	}

	// API documentation route (if using Swagger)
	router.GET("/docs/*any", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "API documentation endpoint",
			"docs":    "Coming soon",
		})
	})

	// Catch-all route for undefined endpoints
	router.NoRoute(func(c *gin.Context) {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Endpoint not found",
			"path":    c.Request.URL.Path,
		})
	})
}
