package handlers

import (
	"customer-backend/internal/services"
	"customer-backend/internal/types"
	"customer-backend/pkg/logger"
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// ShopHandler handles shop-related HTTP requests
type ShopHandler struct {
	shopService *services.ShopService
	logger      *logger.Logger
}

// NewShopHandler creates a new shop handler
func NewShopHandler(shopService *services.ShopService, logger *logger.Logger) *ShopHandler {
	return &ShopHandler{
		shopService: shopService,
		logger:      logger,
	}
}

// GetShops godoc
// @Summary Get shops list
// @Description Get a paginated list of shops with optional filters
// @Tags shops
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param search query string false "Search query"
// @Param cuisine_type query []string false "Cuisine types"
// @Param price_range query []string false "Price ranges"
// @Param min_rating query number false "Minimum rating"
// @Param is_open query bool false "Filter by open status"
// @Param latitude query number false "Latitude for location-based search"
// @Param longitude query number false "Longitude for location-based search"
// @Param radius query number false "Search radius in kilometers"
// @Success 200 {object} types.ShopListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops [get]
func (h *ShopHandler) GetShops(c *gin.Context) {
	// Temporary: Return a simple success response to test the route
	response := types.CreateCustomerResponse([]types.CustomerShopSettings{}, "Shops retrieved successfully", &types.PaginationInfo{
		CurrentPage:  1,
		TotalPages:   1,
		TotalItems:   0,
		ItemsPerPage: 20,
		HasNext:      false,
		HasPrev:      false,
	})

	c.JSON(http.StatusOK, response)
}

// GetShop godoc
// @Summary Get shop details
// @Description Get detailed information about a specific shop
// @Tags shops
// @Accept json
// @Produce json
// @Param id path string true "Shop ID"
// @Success 200 {object} types.ShopDetailResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 404 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/{id} [get]
func (h *ShopHandler) GetShop(c *gin.Context) {
	shopID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid shop ID")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid shop ID"))
		return
	}

	shop, err := h.shopService.GetShopSettings(c.Request.Context(), shopID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get shop")
		c.JSON(http.StatusNotFound, types.CreateErrorResponse("Shop not found"))
		return
	}

	response := &types.ShopDetailResponse{}
	response.CustomerResponse = types.CreateCustomerResponse(
		struct {
			Shop types.CustomerShopSettings `json:"shop"`
		}{Shop: *shop},
		"Shop retrieved successfully",
		nil,
	)

	c.JSON(http.StatusOK, response)
}

// GetNearbyShops godoc
// @Summary Get nearby shops
// @Description Get shops near a specific location
// @Tags shops
// @Accept json
// @Produce json
// @Param latitude query number true "Latitude"
// @Param longitude query number true "Longitude"
// @Param radius query number false "Search radius in kilometers" default(5)
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.ShopListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/nearby [get]
func (h *ShopHandler) GetNearbyShops(c *gin.Context) {
	latStr := c.Query("latitude")
	lngStr := c.Query("longitude")
	radiusStr := c.DefaultQuery("radius", "5")

	if latStr == "" || lngStr == "" {
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Latitude and longitude are required"))
		return
	}

	latitude, err := strconv.ParseFloat(latStr, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid latitude"))
		return
	}

	longitude, err := strconv.ParseFloat(lngStr, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid longitude"))
		return
	}

	radius, err := strconv.ParseFloat(radiusStr, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid radius"))
		return
	}

	var filters types.ShopFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.shopService.GetNearbyShops(c.Request.Context(), latitude, longitude, radius, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get nearby shops")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get nearby shops"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// SearchShops godoc
// @Summary Search shops
// @Description Search for shops based on query string
// @Tags shops
// @Accept json
// @Produce json
// @Param q query string true "Search query"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.ShopListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/search [get]
func (h *ShopHandler) SearchShops(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Search query is required"))
		return
	}

	var filters types.ShopFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.shopService.SearchShops(c.Request.Context(), query, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to search shops")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to search shops"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetPopularShops godoc
// @Summary Get popular shops
// @Description Get a list of popular shops based on ratings
// @Tags shops
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.ShopListResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/popular [get]
func (h *ShopHandler) GetPopularShops(c *gin.Context) {
	var filters types.ShopFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.shopService.GetPopularShops(c.Request.Context(), filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get popular shops")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get popular shops"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetShopsByCategory godoc
// @Summary Get shops by category
// @Description Get shops filtered by cuisine category
// @Tags shops
// @Accept json
// @Produce json
// @Param category path string true "Cuisine category"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.ShopListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/category/{category} [get]
func (h *ShopHandler) GetShopsByCategory(c *gin.Context) {
	category := c.Param("category")
	if category == "" {
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Category is required"))
		return
	}

	var filters types.ShopFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.shopService.GetShopsByCategory(c.Request.Context(), category, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get shops by category")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get shops by category"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetShopStatus godoc
// @Summary Get shop operating status
// @Description Check if a shop is currently open
// @Tags shops
// @Accept json
// @Produce json
// @Param id path string true "Shop ID"
// @Success 200 {object} types.CustomerResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 404 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/{id}/status [get]
func (h *ShopHandler) GetShopStatus(c *gin.Context) {
	shopID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid shop ID")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid shop ID"))
		return
	}

	isOpen, err := h.shopService.GetShopOperatingStatus(c.Request.Context(), shopID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get shop status")
		c.JSON(http.StatusNotFound, types.CreateErrorResponse("Shop not found"))
		return
	}

	response := types.CreateCustomerResponse(
		struct {
			IsOpen bool `json:"is_open"`
		}{IsOpen: isOpen},
		"Shop status retrieved successfully",
		nil,
	)

	c.JSON(http.StatusOK, response)
}
