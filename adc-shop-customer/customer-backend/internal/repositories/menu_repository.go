package repositories

import (
	"context"
	"customer-backend/internal/types"
	"customer-backend/pkg/logger"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// MenuRepository handles menu-related database operations for customers
type MenuRepository struct {
	db     *gorm.DB
	logger *logger.Logger
}

// NewMenuRepository creates a new menu repository
func NewMenuRepository(db *gorm.DB, logger *logger.Logger) *MenuRepository {
	return &MenuRepository{
		db:     db,
		logger: logger,
	}
}

// GetMenuItems retrieves menu items based on customer filters
func (r *MenuRepository) GetMenuItems(ctx context.Context, shopID uuid.UUID, filters types.MenuFilters) ([]types.CustomerMenuItem, int64, error) {
	// First, get the branch ID for the shop (assuming we're using the first branch)
	var branchID uuid.UUID
	err := r.db.WithContext(ctx).
		Table("shop_branches").
		Select("id").
		Where("shop_id = ? AND is_active = ?", shopID, true).
		First(&branchID).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to find branch for shop: %w", err)
	}

	query := r.db.WithContext(ctx).
		Table("menu_items").
		Where("branch_id = ? AND is_available = ?", branchID, true)

	// Apply filters
	query = r.applyMenuFilters(query, filters)

	// Count total records
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count menu items: %w", err)
	}

	// Apply sorting and pagination
	query = r.applyMenuSortingAndPagination(query, filters.CustomerFilters)

	var items []struct {
		ID          uuid.UUID `gorm:"column:id"`
		Name        string    `gorm:"column:name"`
		Description string    `gorm:"column:description"`
		Price       float64   `gorm:"column:price"`
		Images      []string  `gorm:"column:images;type:jsonb"`
		IsAvailable bool      `gorm:"column:is_available"`

		// Dietary information
		IsVegetarian bool `gorm:"column:is_vegetarian"`
		IsVegan      bool `gorm:"column:is_vegan"`
		IsGlutenFree bool `gorm:"column:is_gluten_free"`
		IsSpicy      bool `gorm:"column:is_spicy"`
		SpiceLevel   int  `gorm:"column:spice_level"`

		// Nutritional info
		PreparationTime *int `gorm:"column:preparation_time"`

		// Tags and allergens (stored as JSONB)
		Tags      []string `gorm:"column:tags;type:jsonb"`
		Allergens []string `gorm:"column:allergens;type:jsonb"`

		// Category info
		CategoryID   *uuid.UUID `gorm:"column:category_id"`
		CategoryName string     `gorm:"column:category_name"`
	}

	err = query.
		Select(`
			menu_items.id,
			menu_items.name,
			menu_items.description,
			menu_items.price,
			menu_items.images,
			menu_items.is_available,
			menu_items.is_vegetarian,
			menu_items.is_vegan,
			menu_items.is_gluten_free,
			menu_items.is_spicy,
			menu_items.spice_level,
			menu_items.preparation_time,
			menu_items.tags,
			menu_items.allergens,
			menu_items.category_id,
			COALESCE(menu_categories.name, 'Uncategorized') as category_name
		`).
		Joins("LEFT JOIN menu_categories ON menu_items.category_id = menu_categories.id").
		Find(&items).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get menu items: %w", err)
	}

	// Convert to customer menu items
	customerItems := make([]types.CustomerMenuItem, len(items))
	for i, item := range items {
		// Get the first image or use a default
		var image string
		if len(item.Images) > 0 {
			image = item.Images[0]
		} else {
			image = "/placeholder-food.jpg"
		}

		customerItems[i] = types.CustomerMenuItem{
			ID:              item.ID.String(),
			Name:            item.Name,
			Description:     item.Description,
			Price:           item.Price,
			Image:           image,
			Category:        item.CategoryName,
			IsAvailable:     item.IsAvailable,
			IsPopular:       false, // Will be determined by business logic
			IsNew:           false, // Will be determined by business logic
			IsVegetarian:    item.IsVegetarian,
			IsVegan:         item.IsVegan,
			IsGlutenFree:    item.IsGlutenFree,
			IsSpicy:         item.IsSpicy,
			SpicyLevel:      item.SpiceLevel,
			PreparationTime: item.PreparationTime,
			Tags:            item.Tags,
			Allergens:       item.Allergens,
			Rating:          0.0, // Will be calculated from reviews
			ReviewCount:     0,   // Will be calculated from reviews
		}

		// Load customizations if needed
		if r.hasCustomizations(ctx, item.ID) {
			customerItems[i].HasCustomizations = true
			customizations, err := r.getMenuItemCustomizations(ctx, item.ID)
			if err == nil {
				customerItems[i].Customizations = customizations
			}
		}
	}

	return customerItems, total, nil
}

// GetMenuItem retrieves a single menu item by ID
func (r *MenuRepository) GetMenuItem(ctx context.Context, itemID uuid.UUID) (*types.CustomerMenuItem, error) {
	var item types.CustomerMenuItem

	err := r.db.WithContext(ctx).
		Table("menu_items").
		Where("id = ? AND is_active = ?", itemID, true).
		First(&item).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("menu item not found")
		}
		return nil, fmt.Errorf("failed to get menu item: %w", err)
	}

	// Load customizations
	if r.hasCustomizations(ctx, itemID) {
		item.HasCustomizations = true
		customizations, err := r.getMenuItemCustomizations(ctx, itemID)
		if err == nil {
			item.Customizations = customizations
		}
	}

	return &item, nil
}

// GetMenuCategories retrieves menu categories for a shop
func (r *MenuRepository) GetMenuCategories(ctx context.Context, shopID uuid.UUID) ([]types.MenuCategory, error) {
	// First, get the branch ID for the shop
	var branchID uuid.UUID
	err := r.db.WithContext(ctx).
		Table("shop_branches").
		Select("id").
		Where("shop_id = ? AND is_active = ?", shopID, true).
		First(&branchID).Error

	if err != nil {
		return nil, fmt.Errorf("failed to find branch for shop: %w", err)
	}

	var categories []types.MenuCategory

	err = r.db.WithContext(ctx).
		Table("menu_categories").
		Where("branch_id = ? AND is_active = ?", branchID, true).
		Order("sort_order ASC, name ASC").
		Find(&categories).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get menu categories: %w", err)
	}

	// Count items in each category
	for i := range categories {
		var count int64
		r.db.WithContext(ctx).
			Table("menu_items").
			Where("category_id = ? AND is_available = ?", categories[i].ID, true).
			Count(&count)
		categories[i].ItemCount = int(count)
	}

	return categories, nil
}

// Helper methods

func (r *MenuRepository) applyMenuFilters(query *gorm.DB, filters types.MenuFilters) *gorm.DB {
	// Search filter
	if filters.Search != "" {
		searchPattern := "%" + filters.Search + "%"
		query = query.Where("(name ILIKE ? OR description ILIKE ?)", searchPattern, searchPattern)
	}

	// Category filter
	if filters.CategoryID != nil {
		query = query.Where("category_id = ?", *filters.CategoryID)
	}

	if len(filters.Categories) > 0 {
		query = query.Where("category IN ?", filters.Categories)
	}

	// Availability filter
	if filters.IsAvailable != nil {
		query = query.Where("is_available = ?", *filters.IsAvailable)
	}

	// Popular items filter
	if filters.IsPopular != nil {
		query = query.Where("is_popular = ?", *filters.IsPopular)
	}

	// New items filter
	if filters.IsNew != nil {
		query = query.Where("is_new = ?", *filters.IsNew)
	}

	// Dietary filters
	if filters.IsVegetarian != nil {
		query = query.Where("is_vegetarian = ?", *filters.IsVegetarian)
	}

	if filters.IsVegan != nil {
		query = query.Where("is_vegan = ?", *filters.IsVegan)
	}

	if filters.IsGlutenFree != nil {
		query = query.Where("is_gluten_free = ?", *filters.IsGlutenFree)
	}

	if filters.IsSpicy != nil {
		query = query.Where("is_spicy = ?", *filters.IsSpicy)
	}

	if filters.MaxSpicyLevel != nil {
		query = query.Where("spicy_level <= ?", *filters.MaxSpicyLevel)
	}

	// Price range filter
	if filters.PriceMin != nil {
		query = query.Where("price >= ?", *filters.PriceMin)
	}

	if filters.PriceMax != nil {
		query = query.Where("price <= ?", *filters.PriceMax)
	}

	// Nutritional filters
	if filters.MaxCalories != nil {
		query = query.Where("calories <= ?", *filters.MaxCalories)
	}

	if filters.MaxPrepTime != nil {
		query = query.Where("preparation_time <= ?", *filters.MaxPrepTime)
	}

	// Rating filter
	if filters.MinRating != nil {
		query = query.Where("rating >= ?", *filters.MinRating)
	}

	// Allergen exclusion filter
	if len(filters.Allergens) > 0 {
		for _, allergen := range filters.Allergens {
			query = query.Where("NOT (allergens @> ?)", fmt.Sprintf(`["%s"]`, allergen))
		}
	}

	return query
}

func (r *MenuRepository) applyMenuSortingAndPagination(query *gorm.DB, filters types.CustomerFilters) *gorm.DB {
	// Apply sorting
	sortBy := filters.SortBy
	if sortBy == "" {
		sortBy = "is_popular DESC, rating DESC, name"
	}

	sortOrder := strings.ToUpper(filters.SortOrder)
	if sortOrder != "ASC" && sortOrder != "DESC" {
		sortOrder = "ASC"
	}

	if !strings.Contains(sortBy, "DESC") && !strings.Contains(sortBy, "ASC") {
		query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))
	} else {
		query = query.Order(sortBy)
	}

	// Apply pagination
	if filters.Page > 0 && filters.Limit > 0 {
		offset := (filters.Page - 1) * filters.Limit
		query = query.Offset(offset).Limit(filters.Limit)
	}

	return query
}

func (r *MenuRepository) hasCustomizations(ctx context.Context, itemID uuid.UUID) bool {
	var count int64
	r.db.WithContext(ctx).
		Table("menu_item_customizations").
		Where("menu_item_id = ? AND is_active = ?", itemID, true).
		Count(&count)
	return count > 0
}

func (r *MenuRepository) getMenuItemCustomizations(ctx context.Context, itemID uuid.UUID) ([]types.MenuItemCustomization, error) {
	var customizations []types.MenuItemCustomization

	err := r.db.WithContext(ctx).
		Table("menu_item_customizations").
		Where("menu_item_id = ? AND is_active = ?", itemID, true).
		Order("sort_order ASC").
		Find(&customizations).Error

	if err != nil {
		return nil, err
	}

	// Load options for each customization
	for i := range customizations {
		options, err := r.getCustomizationOptions(ctx, customizations[i].ID)
		if err == nil {
			customizations[i].Options = options
		}
	}

	return customizations, nil
}

func (r *MenuRepository) getCustomizationOptions(ctx context.Context, customizationID uuid.UUID) ([]types.MenuItemCustomizationOption, error) {
	var options []types.MenuItemCustomizationOption

	err := r.db.WithContext(ctx).
		Table("menu_item_customization_options").
		Where("customization_id = ? AND is_available = ?", customizationID, true).
		Order("sort_order ASC").
		Find(&options).Error

	return options, err
}
