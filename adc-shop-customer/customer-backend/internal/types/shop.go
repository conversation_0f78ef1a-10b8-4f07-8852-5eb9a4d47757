package types

import (
	"time"

	"github.com/google/uuid"
)

// CustomerShopSettings represents shop settings optimized for customer view
type CustomerShopSettings struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Logo        string    `json:"logo"`
	CoverImage  string    `json:"cover_image"`

	// Business info
	CuisineType string  `json:"cuisine_type"`
	PriceRange  string  `json:"price_range"`
	Rating      float64 `json:"rating"`
	ReviewCount int     `json:"review_count"`

	// Contact info
	Phone   string  `json:"phone"`
	Email   string  `json:"email"`
	Website string  `json:"website"`
	Address Address `json:"address"`

	// Business hours
	BusinessHours BusinessHours `json:"business_hours"`
	Timezone      string        `json:"timezone"`

	// Customer-relevant settings
	Theme                CustomerTheme    `json:"theme"`
	Features             CustomerFeatures `json:"features"`
	PaymentMethods       []string         `json:"payment_methods"`
	Currency             string           `json:"currency"`
	TaxRate              float64          `json:"tax_rate"`
	ServiceChargeRate    float64          `json:"service_charge_rate"`
	DefaultTipPercentage float64          `json:"default_tip_percentage"`

	// Social media
	SocialMedia SocialMediaLinks `json:"social_media"`

	// Operational status
	IsOpen    bool      `json:"is_open"`
	IsActive  bool      `json:"is_active"`
	UpdatedAt time.Time `json:"updated_at"`
}

// Address represents address information
type Address struct {
	Street     string  `json:"street"`
	City       string  `json:"city"`
	State      string  `json:"state"`
	PostalCode string  `json:"postal_code"`
	Country    string  `json:"country"`
	Latitude   float64 `json:"latitude,omitempty"`
	Longitude  float64 `json:"longitude,omitempty"`
}

// BusinessHours represents business hours for each day
type BusinessHours map[string]string

// CustomerTheme represents theme settings visible to customers
type CustomerTheme struct {
	PrimaryColor    string `json:"primary_color"`
	SecondaryColor  string `json:"secondary_color"`
	AccentColor     string `json:"accent_color"`
	BackgroundColor string `json:"background_color"`
	TextColor       string `json:"text_color"`
	FontFamily      string `json:"font_family"`
	LogoPosition    string `json:"logo_position"`
}

// CustomerFeatures represents features available to customers
type CustomerFeatures struct {
	OnlineOrdering    bool `json:"online_ordering"`
	TableReservations bool `json:"table_reservations"`
	QRMenu            bool `json:"qr_menu"`
	Reviews           bool `json:"reviews"`
	DeliveryEnabled   bool `json:"delivery_enabled"`
	PickupEnabled     bool `json:"pickup_enabled"`
	LoyaltyProgram    bool `json:"loyalty_program"`
	GiftCards         bool `json:"gift_cards"`
}

// SocialMediaLinks represents social media links
type SocialMediaLinks struct {
	Facebook  string `json:"facebook,omitempty"`
	Instagram string `json:"instagram,omitempty"`
	Twitter   string `json:"twitter,omitempty"`
	LinkedIn  string `json:"linkedin,omitempty"`
	TikTok    string `json:"tiktok,omitempty"`
	YouTube   string `json:"youtube,omitempty"`
}

// ShopFilters represents filters for shop queries
type ShopFilters struct {
	CustomerFilters

	// Shop-specific filters
	CuisineType []string `form:"cuisine_type" json:"cuisine_type"`
	PriceRange  []string `form:"price_range" json:"price_range"`
	MinRating   *float64 `form:"min_rating" json:"min_rating"`
	IsOpen      *bool    `form:"is_open" json:"is_open"`
	HasDelivery *bool    `form:"has_delivery" json:"has_delivery"`
	HasPickup   *bool    `form:"has_pickup" json:"has_pickup"`

	// Location-based filters
	Latitude  *float64 `form:"latitude" json:"latitude"`
	Longitude *float64 `form:"longitude" json:"longitude"`
	Radius    *float64 `form:"radius" json:"radius"` // in kilometers
}

// ShopListResponse represents the response for shop list queries
type ShopListResponse struct {
	CustomerResponse
	Data struct {
		Shops []CustomerShopSettings `json:"shops"`
	} `json:"data"`
}

// ShopDetailResponse represents the response for shop detail queries
type ShopDetailResponse struct {
	CustomerResponse
	Data struct {
		Shop CustomerShopSettings `json:"shop"`
	} `json:"data"`
}

// ShopFilterOptions represents available filter options for shops
type ShopFilterOptions struct {
	CuisineTypes []string `json:"cuisine_types"`
	PriceRanges  []string `json:"price_ranges"`
	Features     []string `json:"features"`
}
