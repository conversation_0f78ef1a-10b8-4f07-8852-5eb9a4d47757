# Customer Backend Makefile

# Variables
BINARY_NAME=customer-api
MAIN_PATH=./cmd/server
BUILD_DIR=./bin
GO_FILES=$(shell find . -name "*.go" -type f)

# Default target
.PHONY: all
all: clean build

# Build the application
.PHONY: build
build:
	@echo "Building $(BINARY_NAME)..."
	@mkdir -p $(BUILD_DIR)
	@go build -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PATH)
	@echo "Build complete: $(BUILD_DIR)/$(BINARY_NAME)"

# Run the application
.PHONY: run
run:
	@echo "Running $(BINARY_NAME)..."
	@go run $(MAIN_PATH)

# Run with hot reload (requires air)
.PHONY: dev
dev:
	@echo "Starting development server with hot reload..."
	@air

# Clean build artifacts
.PHONY: clean
clean:
	@echo "Cleaning build artifacts..."
	@rm -rf $(BUILD_DIR)
	@go clean

# Run tests
.PHONY: test
test:
	@echo "Running tests..."
	@go test -v ./...

# Run tests with coverage
.PHONY: test-coverage
test-coverage:
	@echo "Running tests with coverage..."
	@go test -v -coverprofile=coverage.out ./...
	@go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Format code
.PHONY: fmt
fmt:
	@echo "Formatting code..."
	@go fmt ./...

# Lint code
.PHONY: lint
lint:
	@echo "Linting code..."
	@golangci-lint run

# Tidy dependencies
.PHONY: tidy
tidy:
	@echo "Tidying dependencies..."
	@go mod tidy

# Download dependencies
.PHONY: deps
deps:
	@echo "Downloading dependencies..."
	@go mod download

# Install development tools
.PHONY: install-tools
install-tools:
	@echo "Installing development tools..."
	@go install github.com/cosmtrek/air@latest
	@go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# Docker build
.PHONY: docker-build
docker-build:
	@echo "Building Docker image..."
	@docker build -t customer-api:latest .

# Docker run
.PHONY: docker-run
docker-run:
	@echo "Running Docker container..."
	@docker run -p 8081:8081 --env-file .env customer-api:latest

# Generate API documentation (if using swag)
.PHONY: docs
docs:
	@echo "Generating API documentation..."
	@swag init -g cmd/server/main.go -o ./docs

# Database migration (if needed)
.PHONY: migrate
migrate:
	@echo "Running database migrations..."
	@go run ./cmd/migrate

# Health check
.PHONY: health
health:
	@echo "Checking application health..."
	@curl -f http://localhost:8081/health || echo "Health check failed"

# Load test (requires hey or similar tool)
.PHONY: load-test
load-test:
	@echo "Running load test..."
	@hey -n 1000 -c 10 http://localhost:8081/health

# Help
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  build         - Build the application"
	@echo "  run           - Run the application"
	@echo "  dev           - Run with hot reload"
	@echo "  clean         - Clean build artifacts"
	@echo "  test          - Run tests"
	@echo "  test-coverage - Run tests with coverage"
	@echo "  fmt           - Format code"
	@echo "  lint          - Lint code"
	@echo "  tidy          - Tidy dependencies"
	@echo "  deps          - Download dependencies"
	@echo "  install-tools - Install development tools"
	@echo "  docker-build  - Build Docker image"
	@echo "  docker-run    - Run Docker container"
	@echo "  docs          - Generate API documentation"
	@echo "  migrate       - Run database migrations"
	@echo "  health        - Check application health"
	@echo "  load-test     - Run load test"
	@echo "  help          - Show this help"
