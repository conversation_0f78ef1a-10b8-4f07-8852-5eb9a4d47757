'use client';

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react';

const MapTest: React.FC = () => {
  const [apiKeyStatus, setApiKeyStatus] = useState<'checking' | 'valid' | 'invalid' | 'missing'>('checking');
  const [mapsLoaded, setMapsLoaded] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const testGoogleMapsAPI = async () => {
      try {
        // Check if API key is configured
        const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
        
        if (!apiKey || apiKey === 'your_google_maps_api_key_here') {
          setApiKeyStatus('missing');
          return;
        }

        // Try to load Google Maps
        const { Loader } = await import('@googlemaps/js-api-loader');
        
        const loader = new Loader({
          apiKey: apiKey,
          version: 'weekly',
          libraries: ['places', 'geometry'],
        });

        await loader.load();
        setMapsLoaded(true);
        setApiKeyStatus('valid');
        
        // Test a simple geocoding request
        const geocoder = new google.maps.Geocoder();
        geocoder.geocode({ address: 'Bangkok, Thailand' }, (results, status) => {
          if (status === 'OK') {
            console.log('✅ Google Maps API test successful:', results);
          } else {
            console.warn('⚠️ Geocoding test failed:', status);
          }
        });

      } catch (err: any) {
        console.error('❌ Google Maps API test failed:', err);
        setError(err.message);
        setApiKeyStatus('invalid');
      }
    };

    testGoogleMapsAPI();
  }, []);

  const getStatusIcon = () => {
    switch (apiKeyStatus) {
      case 'checking':
        return <AlertCircle className="w-5 h-5 text-yellow-500 animate-pulse" />;
      case 'valid':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'invalid':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'missing':
        return <XCircle className="w-5 h-5 text-orange-500" />;
    }
  };

  const getStatusBadge = () => {
    switch (apiKeyStatus) {
      case 'checking':
        return <Badge variant="secondary">Checking...</Badge>;
      case 'valid':
        return <Badge className="bg-green-100 text-green-800">Valid</Badge>;
      case 'invalid':
        return <Badge variant="destructive">Invalid</Badge>;
      case 'missing':
        return <Badge className="bg-orange-100 text-orange-800">Missing</Badge>;
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {getStatusIcon()}
          Google Maps API Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">API Key Status:</span>
          {getStatusBadge()}
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Maps Loaded:</span>
          <Badge variant={mapsLoaded ? "default" : "secondary"}>
            {mapsLoaded ? "Yes" : "No"}
          </Badge>
        </div>

        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-800 font-medium">Error:</p>
            <p className="text-xs text-red-600 mt-1">{error}</p>
          </div>
        )}

        {apiKeyStatus === 'valid' && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-sm text-green-800 font-medium">✅ Success!</p>
            <p className="text-xs text-green-600 mt-1">
              Google Maps API is working correctly. You can now use all map features.
            </p>
          </div>
        )}

        {apiKeyStatus === 'missing' && (
          <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
            <p className="text-sm text-orange-800 font-medium">⚠️ API Key Missing</p>
            <p className="text-xs text-orange-600 mt-1">
              Please run <code className="bg-orange-100 px-1 rounded">make setup-maps</code> to configure your Google Maps API key.
            </p>
          </div>
        )}

        {apiKeyStatus === 'invalid' && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-800 font-medium">❌ API Key Invalid</p>
            <p className="text-xs text-red-600 mt-1">
              The API key may be incorrect or the required APIs are not enabled. Check the console for details.
            </p>
          </div>
        )}

        <div className="text-xs text-gray-500 space-y-1">
          <p><strong>API Key:</strong> {process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY ? 
            `${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY.substring(0, 10)}...` : 
            'Not configured'
          }</p>
          <p><strong>Environment:</strong> {process.env.NODE_ENV}</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default MapTest;
