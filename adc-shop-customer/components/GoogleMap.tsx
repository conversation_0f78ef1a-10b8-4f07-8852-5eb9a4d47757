'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import {
  loadGoogleMaps,
  DEFAULT_MAP_OPTIONS,
  SHOP_MARKER_ICON,
  USER_MARKER_ICON,
  fitMapToBounds,
  getCurrentLocation,
  ShopLocation,
  GOOGLE_MAPS_CONFIG
} from '@/lib/google-maps';
import MapPlaceholder from './MapPlaceholder';

interface GoogleMapProps {
  shops?: ShopLocation[];
  center?: { lat: number; lng: number };
  zoom?: number;
  height?: string;
  width?: string;
  showUserLocation?: boolean;
  onShopClick?: (shop: ShopLocation) => void;
  onMapClick?: (lat: number, lng: number) => void;
  className?: string;
}

interface MarkerInfo {
  marker: google.maps.Marker;
  shop?: ShopLocation;
}

const GoogleMap: React.FC<GoogleMapProps> = ({
  shops = [],
  center,
  zoom = 15,
  height = '400px',
  width = '100%',
  showUserLocation = true,
  onShopClick,
  onMapClick,
  className = '',
}: GoogleMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<google.maps.Map | null>(null);
  const markersRef = useRef<MarkerInfo[]>([]);
  const userMarkerRef = useRef<google.maps.Marker | null>(null);
  const infoWindowRef = useRef<google.maps.InfoWindow | null>(null);

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);

  // Check if API key is available
  if (!GOOGLE_MAPS_CONFIG.apiKey || GOOGLE_MAPS_CONFIG.apiKey === 'your_google_maps_api_key_here') {
    return <MapPlaceholder height={height} width={width} className={className} />;
  }

  // Initialize map
  const initializeMap = useCallback(async () => {
    if (!mapRef.current) return;

    try {
      setIsLoading(true);
      setError(null);

      const maps = await loadGoogleMaps();

      // Get user location if requested
      let mapCenter = center || DEFAULT_MAP_OPTIONS.center!;

      if (showUserLocation && !center) {
        try {
          const position = await getCurrentLocation();
          const userPos = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };
          setUserLocation(userPos);
          mapCenter = userPos;
        } catch (locationError) {
          console.warn('Could not get user location:', locationError);
        }
      }

      // Create map
      const mapOptions: google.maps.MapOptions = {
        ...DEFAULT_MAP_OPTIONS,
        center: mapCenter,
        zoom,
      };

      const map = new maps.Map(mapRef.current, mapOptions);
      mapInstanceRef.current = map;

      // Create info window
      infoWindowRef.current = new maps.InfoWindow();

      // Add click listener
      if (onMapClick) {
        map.addListener('click', (event: google.maps.MapMouseEvent) => {
          if (event.latLng) {
            onMapClick(event.latLng.lat(), event.latLng.lng());
          }
        });
      }

      setIsLoading(false);
    } catch (err) {
      console.error('Error initializing map:', err);
      setError('Failed to load Google Maps. Please check your API key and internet connection.');
      setIsLoading(false);
    }
  }, [center, zoom, showUserLocation, onMapClick]);

  // Add user location marker
  const addUserLocationMarker = useCallback(async () => {
    if (!mapInstanceRef.current || !userLocation) return;

    const maps = await loadGoogleMaps();

    // Remove existing user marker
    if (userMarkerRef.current) {
      userMarkerRef.current.setMap(null);
    }

    // Create user marker
    const userMarker = new maps.Marker({
      position: userLocation,
      map: mapInstanceRef.current,
      icon: USER_MARKER_ICON,
      title: 'Your Location',
      zIndex: 1000,
    });

    userMarkerRef.current = userMarker;

    // Add click listener for user marker
    userMarker.addListener('click', () => {
      if (infoWindowRef.current) {
        infoWindowRef.current.setContent(`
          <div class="p-2">
            <h3 class="font-semibold text-blue-600">Your Location</h3>
            <p class="text-sm text-gray-600">Lat: ${userLocation.lat.toFixed(6)}</p>
            <p class="text-sm text-gray-600">Lng: ${userLocation.lng.toFixed(6)}</p>
          </div>
        `);
        infoWindowRef.current.open(mapInstanceRef.current, userMarker);
      }
    });
  }, [userLocation]);

  // Add shop markers
  const addShopMarkers = useCallback(async () => {
    if (!mapInstanceRef.current) return;

    const maps = await loadGoogleMaps();

    // Clear existing markers
    markersRef.current.forEach(({ marker }) => {
      marker.setMap(null);
    });
    markersRef.current = [];

    // Add new markers
    shops.forEach((shop) => {
      const marker = new maps.Marker({
        position: { lat: shop.lat, lng: shop.lng },
        map: mapInstanceRef.current,
        icon: SHOP_MARKER_ICON,
        title: shop.name,
        zIndex: 100,
      });

      // Create info window content
      const infoContent = `
        <div class="p-3 max-w-xs">
          <h3 class="font-semibold text-lg text-gray-800 mb-1">${shop.name}</h3>
          <p class="text-sm text-gray-600 mb-2">${shop.address}</p>
          ${shop.rating ? `
            <div class="flex items-center mb-1">
              <span class="text-yellow-500">★</span>
              <span class="text-sm ml-1">${shop.rating.toFixed(1)}</span>
            </div>
          ` : ''}
          ${shop.cuisineType ? `
            <p class="text-xs text-gray-500 mb-1">Cuisine: ${shop.cuisineType}</p>
          ` : ''}
          ${shop.priceRange ? `
            <p class="text-xs text-gray-500 mb-1">Price: ${shop.priceRange}</p>
          ` : ''}
          ${shop.distance ? `
            <p class="text-xs text-gray-500 mb-2">Distance: ${shop.distance.toFixed(1)}km</p>
          ` : ''}
          ${shop.isOpen !== undefined ? `
            <span class="inline-block px-2 py-1 text-xs rounded ${
              shop.isOpen
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }">
              ${shop.isOpen ? 'Open' : 'Closed'}
            </span>
          ` : ''}
          <div class="mt-2">
            <button
              onclick="window.selectShop('${shop.id}')"
              class="bg-orange-500 text-white px-3 py-1 rounded text-sm hover:bg-orange-600"
            >
              View Details
            </button>
          </div>
        </div>
      `;

      // Add click listener
      marker.addListener('click', () => {
        if (infoWindowRef.current) {
          infoWindowRef.current.setContent(infoContent);
          infoWindowRef.current.open(mapInstanceRef.current, marker);
        }

        if (onShopClick) {
          onShopClick(shop);
        }
      });

      markersRef.current.push({ marker, shop });
    });

    // Fit map to show all markers
    if (shops.length > 0) {
      const locations = shops.map(shop => ({ lat: shop.lat, lng: shop.lng }));
      if (userLocation) {
        locations.push(userLocation);
      }
      fitMapToBounds(mapInstanceRef.current, locations);
    }
  }, [shops, onShopClick, userLocation]);

  // Global function for shop selection (called from info window)
  useEffect(() => {
    (window as any).selectShop = (shopId: string) => {
      const shop = shops.find(s => s.id === shopId);
      if (shop && onShopClick) {
        onShopClick(shop);
      }
    };

    return () => {
      delete (window as any).selectShop;
    };
  }, [shops, onShopClick]);

  // Initialize map on mount
  useEffect(() => {
    initializeMap();
  }, [initializeMap]);

  // Update user location marker
  useEffect(() => {
    if (userLocation) {
      addUserLocationMarker();
    }
  }, [userLocation, addUserLocationMarker]);

  // Update shop markers when shops change
  useEffect(() => {
    if (!isLoading && !error) {
      addShopMarkers();
    }
  }, [shops, isLoading, error, addShopMarkers]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      markersRef.current.forEach(({ marker }) => {
        marker.setMap(null);
      });
      if (userMarkerRef.current) {
        userMarkerRef.current.setMap(null);
      }
      if (infoWindowRef.current) {
        infoWindowRef.current.close();
      }
    };
  }, []);

  if (error) {
    return (
      <div
        className={`flex items-center justify-center bg-gray-100 border border-gray-300 rounded-lg ${className}`}
        style={{ height, width }}
      >
        <div className="text-center p-4">
          <div className="text-red-500 mb-2">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-800 mb-1">Map Error</h3>
          <p className="text-sm text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`} style={{ height, width }}>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 border border-gray-300 rounded-lg z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-2"></div>
            <p className="text-sm text-gray-600">Loading map...</p>
          </div>
        </div>
      )}
      <div
        ref={mapRef}
        className="w-full h-full rounded-lg"
        style={{ height, width }}
      />
    </div>
  );
};

export default GoogleMap;
