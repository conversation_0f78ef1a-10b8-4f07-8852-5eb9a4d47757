#!/bin/bash

# Customer Services Stop Script
# This script stops both the customer backend and frontend services

set -e

echo "🛑 Stopping Customer Services..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Stop services
stop_services() {
    print_header "🛑 Stopping customer services..."
    
    # Stop backend
    if [ -f logs/backend.pid ]; then
        BACKEND_PID=$(cat logs/backend.pid)
        if kill -0 $BACKEND_PID 2>/dev/null; then
            kill $BACKEND_PID
            print_status "Stopped customer backend (PID: $BACKEND_PID)"
        else
            print_warning "Backend process not running"
        fi
        rm logs/backend.pid
    else
        print_warning "Backend PID file not found"
    fi
    
    # Stop frontend
    if [ -f logs/frontend.pid ]; then
        FRONTEND_PID=$(cat logs/frontend.pid)
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            kill $FRONTEND_PID
            print_status "Stopped customer frontend (PID: $FRONTEND_PID)"
        else
            print_warning "Frontend process not running"
        fi
        rm logs/frontend.pid
    else
        print_warning "Frontend PID file not found"
    fi
    
    # Kill any remaining processes on the ports
    print_status "Checking for remaining processes on ports 3000 and 8081..."
    
    # Kill processes on port 3000 (frontend)
    if lsof -ti:3000 >/dev/null 2>&1; then
        print_warning "Found processes on port 3000, terminating..."
        lsof -ti:3000 | xargs kill -9 2>/dev/null || true
    fi
    
    # Kill processes on port 8081 (backend)
    if lsof -ti:8081 >/dev/null 2>&1; then
        print_warning "Found processes on port 8081, terminating..."
        lsof -ti:8081 | xargs kill -9 2>/dev/null || true
    fi
    
    print_status "All customer services stopped ✅"
}

# Main execution
main() {
    print_header "🏪 Customer Services Stop"
    echo ""
    
    stop_services
    
    echo ""
    print_status "Customer services have been stopped successfully!"
    echo ""
    echo "📝 Logs are still available in:"
    echo "   Backend: logs/backend.log"
    echo "   Frontend: logs/frontend.log"
    echo ""
    echo "🚀 To start services again, run: ./start-customer-services.sh"
}

# Run main function
main "$@"
