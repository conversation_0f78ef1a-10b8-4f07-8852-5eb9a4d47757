"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Elements } from '@stripe/react-stripe-js';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ArrowLeft } from "lucide-react";
import Header from "@/components/Header";
import { useCart } from "@/lib/context/CartContext";
import { paymentService, PaymentIntent } from "@/lib/services/paymentService";
import CheckoutForm from "@/components/CheckoutForm";

export default function CheckoutPage() {
  const { cartItems, getTotalPrice, clearCart } = useCart();
  const router = useRouter();
  const [paymentIntent, setPaymentIntent] = useState<PaymentIntent | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const tax = subtotal * 0.08;
  const deliveryFee = 2.99;
  const discount = -2.00;
  const total = subtotal + tax + deliveryFee + discount;

  useEffect(() => {
    if (cartItems.length === 0) {
      router.push('/cart');
      return;
    }

    const createPaymentIntent = async () => {
      try {
        setLoading(true);
        const result = await paymentService.createPaymentIntent({
          amount: Math.round(total * 100), // Convert to cents
          currency: 'usd',
          orderItems: cartItems.map(item => ({
            id: item.id,
            name: item.name,
            quantity: item.quantity,
            price: item.price,
          })),
          customerInfo: {
            name: 'Customer', // This would come from a form
            email: '<EMAIL>', // This would come from a form
          },
          metadata: {
            orderType: 'delivery',
          },
        });

        if (result.success && result.paymentIntent) {
          setPaymentIntent(result.paymentIntent);
        } else {
          setError(result.error || 'Failed to create payment intent');
        }
      } catch (err) {
        setError('Failed to initialize payment');
        console.error('Payment intent creation error:', err);
      } finally {
        setLoading(false);
      }
    };

    createPaymentIntent();
  }, [cartItems, total, router]);

  const handlePaymentSuccess = () => {
    clearCart();
    router.push('/orders?payment=success');
  };

  const handlePaymentError = (error: string) => {
    setError(error);
  };

  if (loading) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-background">
        <div className="layout-container flex h-full grow flex-col">
          <Header />
          <div className="px-40 flex flex-1 justify-center py-5">
            <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
              <div className="flex items-center justify-center py-16">
                <p className="text-foreground text-lg">Loading payment...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-background">
        <div className="layout-container flex h-full grow flex-col">
          <Header />
          <div className="px-40 flex flex-1 justify-center py-5">
            <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
              <div className="flex items-center justify-center py-16">
                <Card className="w-full max-w-md">
                  <CardContent className="pt-6">
                    <p className="text-red-600 text-center mb-4">{error}</p>
                    <Button 
                      onClick={() => router.push('/cart')} 
                      className="w-full"
                      variant="outline"
                    >
                      <ArrowLeft className="h-4 w-4 mr-2" />
                      Back to Cart
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-background">
      <div className="layout-container flex h-full grow flex-col">
        <Header />

        {/* Main Content */}
        <div className="px-40 flex flex-1 justify-center py-5">
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
            <div className="flex flex-wrap justify-between gap-3 p-4">
              <div className="flex items-center gap-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => router.push('/cart')}
                  className="p-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
                <p className="text-foreground tracking-light text-[32px] font-bold leading-tight">Checkout</p>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 px-4">
              {/* Payment Form */}
              <div className="space-y-4">
                {paymentIntent && (
                  <Elements 
                    stripe={paymentService.getStripe()} 
                    options={{
                      clientSecret: paymentIntent.clientSecret,
                      appearance: {
                        theme: 'stripe',
                        variables: {
                          colorPrimary: '#e58219',
                          colorBackground: '#ffffff',
                          colorText: '#181510',
                          colorDanger: '#df1b41',
                          fontFamily: 'Inter, system-ui, sans-serif',
                          spacingUnit: '4px',
                          borderRadius: '8px',
                        },
                      },
                    }}
                  >
                    <CheckoutForm
                      paymentIntent={paymentIntent}
                      onSuccess={handlePaymentSuccess}
                      onError={handlePaymentError}
                    />
                  </Elements>
                )}
              </div>

              {/* Order Summary */}
              <div className="space-y-4">
                <Card className="bg-[#f4f2f0] border-[#e5e1dc]">
                  <CardHeader>
                    <CardTitle className="text-[#181510] text-lg">Order Summary</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Order Items */}
                    <div className="space-y-3">
                      {cartItems.map((item) => (
                        <div key={item.id} className="flex justify-between items-center">
                          <div className="flex-1">
                            <p className="text-[#181510] text-sm font-medium">{item.name}</p>
                            <p className="text-[#8a745c] text-xs">Qty: {item.quantity}</p>
                          </div>
                          <p className="text-[#181510] text-sm font-medium">
                            ${(item.price * item.quantity).toFixed(2)}
                          </p>
                        </div>
                      ))}
                    </div>

                    <Separator className="bg-[#e5e1dc]" />

                    {/* Totals */}
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <p className="text-[#8a745c] text-sm">Subtotal</p>
                        <p className="text-[#181510] text-sm">${subtotal.toFixed(2)}</p>
                      </div>
                      <div className="flex justify-between">
                        <p className="text-[#8a745c] text-sm">Tax</p>
                        <p className="text-[#181510] text-sm">${tax.toFixed(2)}</p>
                      </div>
                      <div className="flex justify-between">
                        <p className="text-[#8a745c] text-sm">Delivery Fee</p>
                        <p className="text-[#181510] text-sm">${deliveryFee.toFixed(2)}</p>
                      </div>
                      <div className="flex justify-between">
                        <p className="text-[#8a745c] text-sm">Discount</p>
                        <p className="text-[#181510] text-sm">${discount.toFixed(2)}</p>
                      </div>
                      <Separator className="bg-[#e5e1dc]" />
                      <div className="flex justify-between">
                        <p className="text-[#181510] text-base font-bold">Total</p>
                        <p className="text-[#181510] text-base font-bold">${total.toFixed(2)}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
