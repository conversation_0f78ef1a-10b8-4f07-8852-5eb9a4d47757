import type { Metada<PERSON> } from "next";
import { Inter, Be_Vietnam_Pro } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "next-themes";
import { CartProvider } from "@/lib/context/CartContext";
import { OrderProvider } from "@/lib/context/OrderContext";
import { ReservationProvider } from "@/lib/context/ReservationContext";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

const beVietnamPro = Be_Vietnam_Pro({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
  variable: "--font-be-vietnam-pro",
});

export const metadata: Metadata = {
  title: "ADC Shop - Customer Portal",
  description: "Browse menus, place orders, and manage reservations",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${inter.variable} ${beVietnamPro.variable} antialiased`}
        style={{ fontFamily: '"Be Vietnam Pro", "Noto Sans", sans-serif' }}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <CartProvider>
            <OrderProvider>
              <ReservationProvider>
                {children}
              </ReservationProvider>
            </OrderProvider>
          </CartProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
