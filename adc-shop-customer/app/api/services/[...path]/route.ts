import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth/nextauth.config'

// Routes that require authentication
const AUTHENTICATED_ROUTES = [
  'auth/me',
  'orders',
  'reservations',
  'profile',
  'favorites',
  'reviews',
]

// Public routes that don't require authentication
const PUBLIC_ROUTES = [
  'shops', // Covers shops, shops/{id}, shops/{id}/menu, etc.
  'menu',  // Covers menu/items/{id}, etc.
  'health',
  'auth/login',
  'auth/register',
  'auth/oauth-user',
]

// Check if a route requires authentication
function requiresAuth(path: string): boolean {
  return AUTHENTICATED_ROUTES.some(route => path.startsWith(route))
}

// Check if a route is explicitly public
function isPublicRoute(path: string): boolean {
  return PUBLIC_ROUTES.some(route => path.startsWith(route))
}

// Proxy function to forward requests to the customer backend
async function proxyRequest(
  request: NextRequest,
  path: string,
  session?: { user: { id: string; email: string; role?: string } }
): Promise<NextResponse> {
  const backendUrl = process.env.NEXT_PUBLIC_CUSTOMER_API_URL || 'http://localhost:8081'
  const targetUrl = `${backendUrl}/api/v1/${path}`

  // Prepare headers
  const headers = new Headers()

  // Copy relevant headers from the original request
  const headersToForward = [
    'content-type',
    'accept',
    'user-agent',
    'accept-language',
    'accept-encoding',
  ]

  headersToForward.forEach(headerName => {
    const value = request.headers.get(headerName)
    if (value) {
      headers.set(headerName, value)
    }
  })

  // Add authentication headers if session exists
  if (session?.user) {
    headers.set('X-User-ID', session.user.id)
    headers.set('X-User-Email', session.user.email)
    headers.set('X-User-Role', session.user.role || 'customer')
    headers.set('X-Auth-Source', 'nextauth')
  }

  try {
    // Prepare request options
    const requestOptions: RequestInit = {
      method: request.method,
      headers,
    }

    // Add body for non-GET requests
    if (request.method !== 'GET' && request.method !== 'HEAD') {
      requestOptions.body = await request.text()
    }

    // Make the request to the backend
    const response = await fetch(targetUrl, requestOptions)

    // Get response data
    const responseData = await response.text()

    // Create response with same status and headers
    const nextResponse = new NextResponse(responseData, {
      status: response.status,
      statusText: response.statusText,
    })

    // Copy relevant response headers
    const responseHeadersToForward = [
      'content-type',
      'cache-control',
      'etag',
      'last-modified',
    ]

    responseHeadersToForward.forEach(headerName => {
      const value = response.headers.get(headerName)
      if (value) {
        nextResponse.headers.set(headerName, value)
      }
    })

    return nextResponse
  } catch (error) {
    console.error('Proxy request failed:', error)
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Handle all HTTP methods
async function handleRequest(request: NextRequest, { params }: { params: Promise<{ path: string[] }> }) {
  const resolvedParams = await params
  const path = resolvedParams.path.join('/')

  // Debug logging
  console.log('API Proxy - Path:', path)
  console.log('API Proxy - Requires Auth:', requiresAuth(path))
  console.log('API Proxy - Is Public:', isPublicRoute(path))

  // Check if authentication is required
  if (requiresAuth(path)) {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json(
        {
          success: false,
          message: 'Authentication required',
          error: 'Unauthorized'
        },
        { status: 401 }
      )
    }

    return proxyRequest(request, path, session)
  }

  // For public routes (shops, menu, etc.), don't require auth but include session if available
  // This allows for personalized responses when user is logged in (e.g., favorites, recommendations)
  if (isPublicRoute(path)) {
    const session = await getServerSession(authOptions)
    return proxyRequest(request, path, session)
  }

  // For other routes, still try to get session for optional auth
  const session = await getServerSession(authOptions)
  return proxyRequest(request, path, session)
}

export async function GET(request: NextRequest, context: { params: Promise<{ path: string[] }> }) {
  return handleRequest(request, context)
}

export async function POST(request: NextRequest, context: { params: Promise<{ path: string[] }> }) {
  return handleRequest(request, context)
}

export async function PUT(request: NextRequest, context: { params: Promise<{ path: string[] }> }) {
  return handleRequest(request, context)
}

export async function PATCH(request: NextRequest, context: { params: Promise<{ path: string[] }> }) {
  return handleRequest(request, context)
}

export async function DELETE(request: NextRequest, context: { params: Promise<{ path: string[] }> }) {
  return handleRequest(request, context)
}
