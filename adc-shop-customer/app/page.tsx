"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { CenterPagination, PaginationInfo } from "@/components/ui/pagination";
import { Search, MapPin, Star, ChevronDown } from "lucide-react";
import Header from "@/components/Header";
import { ShopFilters } from "@/lib/services/customerApiClient";
import { useFilteredPagination } from "@/lib/hooks/usePagination";
import { useGetShopsQuery } from "@/lib/store/api/customerApi";
import Link from "next/link";

export default function HomePage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [locationQuery, setLocationQuery] = useState("");
  const [selectedCuisine, setSelectedCuisine] = useState<string>("all");
  const [selectedPriceRange, setSelectedPriceRange] = useState<string>("all");
  const [selectedRating, setSelectedRating] = useState<string>("all");

  // Initialize pagination with filters
  const pagination = useFilteredPagination<ShopFilters>({
    initialPage: 1,
    initialLimit: 20,
    initialFilters: {
      search: "",
      cuisine_type: undefined,
      price_range: undefined,
      min_rating: undefined,
    },
  });

  // Prepare filters for RTK Query
  const filters: ShopFilters = {
    ...pagination.filters,
    page: pagination.currentPage,
    limit: pagination.limit,
    search: searchQuery || undefined,
    cuisine_type: selectedCuisine !== "all" ? [selectedCuisine] : undefined,
    price_range: selectedPriceRange !== "all" ? [selectedPriceRange] : undefined,
    min_rating: selectedRating !== "all" ? parseFloat(selectedRating) : undefined,
  };

  // Use RTK Query to fetch shops
  const {
    data: shopsResponse,
    error,
    isLoading,
    refetch,
  } = useGetShopsQuery(filters);

  // Extract data from response
  const shops = shopsResponse?.data?.shops || [];

  // Update pagination info when data changes
  useEffect(() => {
    if (shopsResponse?.pagination) {
      pagination.setPaginationInfo(shopsResponse.pagination);
    }
  }, [shopsResponse?.pagination, pagination]);

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    pagination.setPage(1);
  };

  // Handle location search
  const handleLocationSearch = (query: string) => {
    setLocationQuery(query);
    // In a real app, this would trigger location-based filtering
  };

  // TODO: Add filter handlers when implementing dropdown functionality

  if (isLoading && shops.length === 0) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-background">
        <div className="layout-container flex h-full grow flex-col">
          <Header />
          <div className="flex flex-1 justify-center items-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading restaurants...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    const errorMessage = 'status' in error
      ? `Error ${error.status}: Failed to load restaurants`
      : error.message || 'Failed to load restaurants';

    return (
      <div className="relative flex size-full min-h-screen flex-col bg-background">
        <div className="layout-container flex h-full grow flex-col">
          <Header />
          <div className="flex flex-1 justify-center items-center">
            <div className="text-center">
              <p className="text-destructive mb-4">{errorMessage}</p>
              <Button onClick={() => refetch()}>Try Again</Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-background">
      <div className="layout-container flex h-full grow flex-col">
        <Header />

        {/* Main Content */}
        <div className="px-40 flex flex-1 justify-center py-5">
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1">

            {/* Hero Section with Search */}
            <div className="px-4 py-8">
              <h1 className="text-foreground tracking-light text-[32px] font-bold leading-tight text-center mb-8">
                Discover Restaurants Near You
              </h1>

              {/* Search Bars */}
              <div className="space-y-4 mb-6">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Search for restaurants or cuisines"
                    value={searchQuery}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10 h-12"
                  />
                </div>

                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Enter an address"
                    value={locationQuery}
                    onChange={(e) => handleLocationSearch(e.target.value)}
                    className="pl-10 h-12"
                  />
                </div>
              </div>

              {/* Map Placeholder */}
              <div className="w-full h-64 bg-[#5a9b8e] rounded-lg mb-6 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-[#5a9b8e] to-[#4a8b7e] opacity-90"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-white text-lg font-medium">Interactive Map</div>
                </div>
                {/* Map controls */}
                <div className="absolute top-4 right-4 flex flex-col gap-2">
                  <Button size="sm" variant="secondary" className="w-8 h-8 p-0">+</Button>
                  <Button size="sm" variant="secondary" className="w-8 h-8 p-0">-</Button>
                  <Button size="sm" variant="secondary" className="w-8 h-8 p-0">📍</Button>
                </div>
              </div>
            </div>

            <h2 className="text-foreground tracking-light text-[28px] font-bold leading-tight px-4 text-left pb-3">
              Restaurants Near You
            </h2>

            {/* Filter Dropdowns */}
            <div className="flex gap-3 p-4 flex-wrap">
              <div className="relative">
                <Button variant="outline" className="h-10 px-4">
                  Cuisine <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </div>
              <div className="relative">
                <Button variant="outline" className="h-10 px-4">
                  Price Range <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </div>
              <div className="relative">
                <Button variant="outline" className="h-10 px-4">
                  Ratings <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Pagination Info */}
            <div className="px-4 pb-3">
              <PaginationInfo
                startItem={pagination.startItem}
                endItem={pagination.endItem}
                totalItems={pagination.totalItems}
              />
            </div>

            {/* Restaurant List */}
            {shops.map((shop) => (
              <div key={shop.id} className="p-4">
                <Link href={`/food/${shop.id}`}>
                  <Card className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                    <div className="flex items-center gap-4 p-4">
                      <div
                        className="w-16 h-16 bg-center bg-no-repeat bg-cover rounded-lg flex-shrink-0"
                        style={{
                          backgroundImage: shop.logo ? `url("${shop.logo}")` : 'none',
                          backgroundColor: shop.logo ? 'transparent' : '#e5ccb2'
                        }}
                      />
                      <div className="flex-1 min-w-0">
                        <CardHeader className="p-0">
                          <CardTitle className="text-lg font-semibold text-foreground">
                            {shop.name}
                          </CardTitle>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                              <span>{shop.rating || "4.5"}</span>
                            </div>
                            <span>•</span>
                            <span>{shop.cuisine_type || "Italian"}</span>
                            <span>•</span>
                            <span>{shop.price_range || "$$"}</span>
                          </div>
                          {shop.description && (
                            <CardDescription className="text-sm mt-1">
                              {shop.description}
                            </CardDescription>
                          )}
                        </CardHeader>
                      </div>
                    </div>
                  </Card>
                </Link>
              </div>
            ))}

            {/* Center Pagination */}
            {pagination.totalPages > 1 && (
              <div className="px-4 py-6">
                <CenterPagination
                  currentPage={pagination.currentPage}
                  totalPages={pagination.totalPages}
                  centerPages={pagination.centerPages}
                  showFirst={pagination.showFirst}
                  showLast={pagination.showLast}
                  onPageChange={pagination.setPage}
                  canGoPrev={pagination.canGoPrev}
                  canGoNext={pagination.canGoNext}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
