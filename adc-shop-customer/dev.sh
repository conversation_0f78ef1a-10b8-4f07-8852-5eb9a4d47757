#!/bin/bash

# ADC Shop Customer Development Script
# Simple script to start both frontend and backend services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Check if required tools are installed
check_dependencies() {
    print_header "🔍 Checking dependencies..."

    if ! command -v go &> /dev/null; then
        print_error "Go is not installed. Please install Go 1.21 or higher."
        exit 1
    fi

    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18 or higher."
        exit 1
    fi

    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm."
        exit 1
    fi

    print_status "All dependencies are installed ✅"
}

# Install dependencies if needed
install_dependencies() {
    print_header "📦 Installing dependencies..."

    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        print_status "Installing frontend dependencies..."
        npm install
    else
        print_status "Frontend dependencies already installed ✅"
    fi

    # Check if backend dependencies are downloaded
    cd customer-backend
    if ! go list -m all &> /dev/null; then
        print_status "Installing backend dependencies..."
        go mod download
    else
        print_status "Backend dependencies already installed ✅"
    fi

    # Install development tools (air, golangci-lint)
    if ! command -v air &> /dev/null; then
        print_status "Installing development tools..."
        make install-tools
    else
        print_status "Development tools already installed ✅"
    fi
    cd ..
}

# Setup environment files
setup_environment() {
    print_header "🔧 Setting up environment..."

    # Customer backend environment
    if [ ! -f "customer-backend/.env" ]; then
        if [ -f "customer-backend/.env.example" ]; then
            print_warning "Customer backend .env file not found. Creating from example..."
            cp customer-backend/.env.example customer-backend/.env
            print_status "Created customer-backend/.env from example"
            print_warning "Please update the database credentials in customer-backend/.env"
        else
            print_warning "No .env.example found in customer-backend/"
        fi
    fi

    # Customer frontend environment
    if [ ! -f ".env.local" ]; then
        if [ -f ".env.local.example" ]; then
            print_warning "Frontend .env.local file not found. Creating from example..."
            cp .env.local.example .env.local
            print_status "Created .env.local from example"
        else
            print_warning "No .env.local.example found"
        fi
    fi
}

# Start services
start_services() {
    print_header "🚀 Starting services..."

    # Create log directory
    mkdir -p logs

    # Start backend in background
    print_status "Starting customer backend on port 8081..."
    cd customer-backend

    # Try to use air for hot reload, fallback to regular go run
    if command -v air &> /dev/null; then
        print_status "Using air for hot reload..."
        air > ../logs/backend.log 2>&1 &
    else
        print_status "Using regular go run..."
        go run cmd/server/main.go > ../logs/backend.log 2>&1 &
    fi

    BACKEND_PID=$!
    cd ..

    # Save backend PID
    echo $BACKEND_PID > logs/backend.pid

    # Wait a moment for backend to start
    sleep 3

    # Check if backend is running
    if kill -0 $BACKEND_PID 2>/dev/null; then
        print_status "Backend started successfully (PID: $BACKEND_PID) ✅"
    else
        print_error "Failed to start backend. Check logs/backend.log for details."
        exit 1
    fi

    print_header "🎉 Services are starting!"
    echo ""
    echo "📱 Frontend: http://localhost:3000 (starting...)"
    echo "🔧 Backend API: http://localhost:8081"
    echo "📊 Health Check: http://localhost:8081/health"
    echo ""
    echo "📝 Backend logs: logs/backend.log"
    echo "🛑 To stop backend: kill $BACKEND_PID"
    echo ""

    # Start frontend (this will block)
    print_status "Starting customer frontend on port 3000..."
    npm run dev
}

# Cleanup function
cleanup() {
    print_header "🧹 Cleaning up..."

    if [ -f logs/backend.pid ]; then
        BACKEND_PID=$(cat logs/backend.pid)
        if kill -0 $BACKEND_PID 2>/dev/null; then
            kill $BACKEND_PID
            print_status "Stopped backend (PID: $BACKEND_PID)"
        fi
        rm logs/backend.pid
    fi

    print_status "Cleanup completed ✅"
}

# Trap cleanup on script exit
trap cleanup EXIT INT TERM

# Show usage
show_usage() {
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  start, dev    - Start both services (default)"
    echo "  frontend      - Start only frontend"
    echo "  backend       - Start only backend"
    echo "  setup         - Setup environment and install dependencies"
    echo "  clean         - Clean logs and stop services"
    echo "  help          - Show this help"
    echo ""
}

# Main execution
main() {
    print_header "🏪 ADC Shop Customer Development"
    echo ""

    case "${1:-start}" in
        "start"|"dev"|"")
            check_dependencies
            setup_environment
            install_dependencies
            start_services
            ;;
        "frontend")
            print_status "Starting frontend only..."
            npm run dev
            ;;
        "backend")
            print_status "Starting backend only..."
            cd customer-backend && go run cmd/server/main.go
            ;;
        "setup")
            check_dependencies
            setup_environment
            install_dependencies
            print_status "Setup completed! Run './dev.sh' to start services."
            ;;
        "clean")
            cleanup
            rm -rf logs
            print_status "Cleaned up logs and stopped services"
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            print_error "Unknown command: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
