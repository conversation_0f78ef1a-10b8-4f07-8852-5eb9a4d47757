# ADC Shop Customer Services Makefile
# This Makefile manages both frontend (Next.js) and backend (Go) services

# Variables
FRONTEND_DIR = .
BACKEND_DIR = customer-backend
LOGS_DIR = logs
FRONTEND_PORT = 3000
BACKEND_PORT = 8081

# Colors for output
RED = \033[0;31m
GREEN = \033[0;32m
YELLOW = \033[1;33m
BLUE = \033[0;34m
NC = \033[0m

# Default target
.PHONY: help
help:
	@echo "$(BLUE)ADC Shop Customer Services$(NC)"
	@echo ""
	@echo "$(GREEN)Available commands:$(NC)"
	@echo "  $(YELLOW)dev$(NC)              - Start both frontend and backend in development mode"
	@echo "  $(YELLOW)dev-frontend$(NC)     - Start only frontend in development mode"
	@echo "  $(YELLOW)dev-backend$(NC)      - Start only backend in development mode"
	@echo "  $(YELLOW)build$(NC)            - Build both frontend and backend"
	@echo "  $(YELLOW)build-frontend$(NC)   - Build only frontend"
	@echo "  $(YELLOW)build-backend$(NC)    - Build only backend"
	@echo "  $(YELLOW)start$(NC)            - Start both services (production mode)"
	@echo "  $(YELLOW)start-frontend$(NC)   - Start only frontend (production mode)"
	@echo "  $(YELLOW)start-backend$(NC)    - Start only backend (production mode)"
	@echo "  $(YELLOW)stop$(NC)             - Stop all running services"
	@echo "  $(YELLOW)restart$(NC)          - Restart all services"
	@echo "  $(YELLOW)clean$(NC)            - Clean build artifacts and logs"
	@echo "  $(YELLOW)install$(NC)          - Install all dependencies"
	@echo "  $(YELLOW)install-frontend$(NC) - Install frontend dependencies"
	@echo "  $(YELLOW)install-backend$(NC)  - Install backend dependencies"
	@echo "  $(YELLOW)install-tools$(NC)    - Install development tools (air, golangci-lint)"
	@echo "  $(YELLOW)test$(NC)             - Run all tests"
	@echo "  $(YELLOW)test-frontend$(NC)    - Run frontend tests"
	@echo "  $(YELLOW)test-backend$(NC)     - Run backend tests"
	@echo "  $(YELLOW)lint$(NC)             - Lint all code"
	@echo "  $(YELLOW)lint-frontend$(NC)    - Lint frontend code"
	@echo "  $(YELLOW)lint-backend$(NC)     - Lint backend code"
	@echo "  $(YELLOW)format$(NC)           - Format all code"
	@echo "  $(YELLOW)health$(NC)           - Check service health"
	@echo "  $(YELLOW)logs$(NC)             - Show live logs"
	@echo "  $(YELLOW)setup$(NC)            - Initial setup (install deps, setup env)"
	@echo ""

# Development commands
.PHONY: dev
dev: setup-logs
	@echo "$(GREEN)🚀 Starting both services in development mode...$(NC)"
	@$(MAKE) dev-backend-bg
	@sleep 3
	@$(MAKE) dev-frontend

.PHONY: dev-frontend
dev-frontend:
	@echo "$(GREEN)🎨 Starting frontend development server...$(NC)"
	@cd $(FRONTEND_DIR) && npm run dev

.PHONY: dev-backend
dev-backend:
	@echo "$(GREEN)⚙️  Starting backend development server...$(NC)"
	@cd $(BACKEND_DIR) && (timeout 5 make dev 2>/dev/null || (echo "$(YELLOW)Air failed, falling back to regular run...$(NC)" && make run))

.PHONY: dev-backend-bg
dev-backend-bg: setup-logs
	@echo "$(GREEN)⚙️  Starting backend development server in background...$(NC)"
	@cd $(BACKEND_DIR) && make run > ../$(LOGS_DIR)/backend.log 2>&1 &
	@echo $$! > $(LOGS_DIR)/backend.pid
	@echo "$(GREEN)Backend started with PID: $$(cat $(LOGS_DIR)/backend.pid)$(NC)"

# Build commands
.PHONY: build
build: build-backend build-frontend

.PHONY: build-frontend
build-frontend:
	@echo "$(GREEN)🔨 Building frontend...$(NC)"
	@cd $(FRONTEND_DIR) && npm run build

.PHONY: build-backend
build-backend:
	@echo "$(GREEN)🔨 Building backend...$(NC)"
	@cd $(BACKEND_DIR) && make build

# Production start commands
.PHONY: start
start: build setup-logs
	@echo "$(GREEN)🚀 Starting both services in production mode...$(NC)"
	@$(MAKE) start-backend-bg
	@sleep 3
	@$(MAKE) start-frontend

.PHONY: start-frontend
start-frontend:
	@echo "$(GREEN)🎨 Starting frontend production server...$(NC)"
	@cd $(FRONTEND_DIR) && npm start

.PHONY: start-backend
start-backend:
	@echo "$(GREEN)⚙️  Starting backend production server...$(NC)"
	@cd $(BACKEND_DIR) && make run

.PHONY: start-backend-bg
start-backend-bg: setup-logs
	@echo "$(GREEN)⚙️  Starting backend production server in background...$(NC)"
	@cd $(BACKEND_DIR) && make run > ../$(LOGS_DIR)/backend.log 2>&1 &
	@echo $$! > $(LOGS_DIR)/backend.pid
	@echo "$(GREEN)Backend started with PID: $$(cat $(LOGS_DIR)/backend.pid)$(NC)"

# Stop services
.PHONY: stop
stop:
	@echo "$(YELLOW)🛑 Stopping all services...$(NC)"
	@if [ -f $(LOGS_DIR)/backend.pid ]; then \
		PID=$$(cat $(LOGS_DIR)/backend.pid); \
		if kill -0 $$PID 2>/dev/null; then \
			kill $$PID && echo "$(GREEN)Stopped backend (PID: $$PID)$(NC)"; \
		fi; \
		rm -f $(LOGS_DIR)/backend.pid; \
	fi
	@if [ -f $(LOGS_DIR)/frontend.pid ]; then \
		PID=$$(cat $(LOGS_DIR)/frontend.pid); \
		if kill -0 $$PID 2>/dev/null; then \
			kill $$PID && echo "$(GREEN)Stopped frontend (PID: $$PID)$(NC)"; \
		fi; \
		rm -f $(LOGS_DIR)/frontend.pid; \
	fi
	@pkill -f "next dev" 2>/dev/null || true
	@pkill -f "customer-api" 2>/dev/null || true
	@echo "$(GREEN)✅ All services stopped$(NC)"

# Restart services
.PHONY: restart
restart: stop dev

# Clean up
.PHONY: clean
clean:
	@echo "$(YELLOW)🧹 Cleaning up...$(NC)"
	@cd $(FRONTEND_DIR) && rm -rf .next node_modules/.cache
	@cd $(BACKEND_DIR) && make clean
	@rm -rf $(LOGS_DIR)
	@echo "$(GREEN)✅ Cleanup completed$(NC)"

# Install dependencies
.PHONY: install
install: install-frontend install-backend install-tools

.PHONY: install-frontend
install-frontend:
	@echo "$(GREEN)📦 Installing frontend dependencies...$(NC)"
	@cd $(FRONTEND_DIR) && npm install

.PHONY: install-backend
install-backend:
	@echo "$(GREEN)📦 Installing backend dependencies...$(NC)"
	@cd $(BACKEND_DIR) && make deps

.PHONY: install-tools
install-tools:
	@echo "$(GREEN)🔧 Installing development tools...$(NC)"
	@cd $(BACKEND_DIR) && make install-tools

# Test commands
.PHONY: test
test: test-frontend test-backend

.PHONY: test-frontend
test-frontend:
	@echo "$(GREEN)🧪 Running frontend tests...$(NC)"
	@cd $(FRONTEND_DIR) && npm test

.PHONY: test-backend
test-backend:
	@echo "$(GREEN)🧪 Running backend tests...$(NC)"
	@cd $(BACKEND_DIR) && make test

# Lint commands
.PHONY: lint
lint: lint-frontend lint-backend

.PHONY: lint-frontend
lint-frontend:
	@echo "$(GREEN)🔍 Linting frontend code...$(NC)"
	@cd $(FRONTEND_DIR) && npm run lint

.PHONY: lint-backend
lint-backend:
	@echo "$(GREEN)🔍 Linting backend code...$(NC)"
	@cd $(BACKEND_DIR) && make lint

# Format commands
.PHONY: format
format: format-frontend format-backend

.PHONY: format-frontend
format-frontend:
	@echo "$(GREEN)✨ Formatting frontend code...$(NC)"
	@cd $(FRONTEND_DIR) && npm run lint -- --fix

.PHONY: format-backend
format-backend:
	@echo "$(GREEN)✨ Formatting backend code...$(NC)"
	@cd $(BACKEND_DIR) && make fmt

# Health check
.PHONY: health
health:
	@echo "$(GREEN)🏥 Checking service health...$(NC)"
	@echo "Frontend (port $(FRONTEND_PORT)):"
	@curl -f http://localhost:$(FRONTEND_PORT) > /dev/null 2>&1 && echo "  ✅ Frontend is healthy" || echo "  ❌ Frontend is not responding"
	@echo "Backend (port $(BACKEND_PORT)):"
	@curl -f http://localhost:$(BACKEND_PORT)/health > /dev/null 2>&1 && echo "  ✅ Backend is healthy" || echo "  ❌ Backend is not responding"

# Show logs
.PHONY: logs
logs:
	@echo "$(GREEN)📝 Showing live logs (Ctrl+C to stop)...$(NC)"
	@if [ -f $(LOGS_DIR)/backend.log ]; then \
		tail -f $(LOGS_DIR)/backend.log; \
	else \
		echo "$(YELLOW)No backend logs found. Start the backend first.$(NC)"; \
	fi

# Setup logs directory
.PHONY: setup-logs
setup-logs:
	@mkdir -p $(LOGS_DIR)

# Initial setup
.PHONY: setup
setup:
	@echo "$(GREEN)🔧 Initial setup...$(NC)"
	@$(MAKE) install
	@$(MAKE) setup-env
	@echo "$(GREEN)✅ Setup completed$(NC)"

# Setup environment files
.PHONY: setup-env
setup-env:
	@echo "$(GREEN)🔧 Setting up environment files...$(NC)"
	@if [ ! -f $(BACKEND_DIR)/.env ] && [ -f $(BACKEND_DIR)/.env.example ]; then \
		cp $(BACKEND_DIR)/.env.example $(BACKEND_DIR)/.env; \
		echo "$(YELLOW)Created $(BACKEND_DIR)/.env from example$(NC)"; \
	fi
	@if [ ! -f $(FRONTEND_DIR)/.env.local ] && [ -f $(FRONTEND_DIR)/.env.local.example ]; then \
		cp $(FRONTEND_DIR)/.env.local.example $(FRONTEND_DIR)/.env.local; \
		echo "$(YELLOW)Created .env.local from example$(NC)"; \
	fi

# Quick start (most common command)
.PHONY: quick-start
quick-start:
	@echo "$(BLUE)🚀 Quick Start - ADC Shop Customer Services$(NC)"
	@echo ""
	@$(MAKE) setup
	@$(MAKE) dev

# Docker commands (if needed)
.PHONY: docker-build
docker-build:
	@echo "$(GREEN)🐳 Building Docker images...$(NC)"
	@cd $(BACKEND_DIR) && make docker-build
	@docker build -t adc-shop-customer-frontend -f Dockerfile.frontend .

.PHONY: docker-run
docker-run:
	@echo "$(GREEN)🐳 Running Docker containers...$(NC)"
	@docker-compose up -d

.PHONY: docker-stop
docker-stop:
	@echo "$(GREEN)🐳 Stopping Docker containers...$(NC)"
	@docker-compose down

# Show service URLs
.PHONY: urls
urls:
	@echo "$(BLUE)🌐 Service URLs:$(NC)"
	@echo "  Frontend: http://localhost:$(FRONTEND_PORT)"
	@echo "  Backend API: http://localhost:$(BACKEND_PORT)"
	@echo "  Health Check: http://localhost:$(BACKEND_PORT)/health"
