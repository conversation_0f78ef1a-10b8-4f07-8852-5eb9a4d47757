# RTK Query and Proxy API Routes Implementation

This document outlines the implementation of RTK Query for API requests and proxy API routes with authentication handling in the customer app.

## Overview

The implementation replaces direct fetch calls to the backend with RTK Query for better state management, caching, and error handling. It also introduces proxy API routes that handle authentication using NextAuth.

## Key Changes

### 1. Dependencies Added
- `@reduxjs/toolkit` - For RTK Query
- `react-redux` - For Redux integration
- `next-auth` - For authentication

### 2. New Files Created

#### Store Configuration
- `lib/store/store.ts` - Redux store configuration
- `lib/store/api/customerApi.ts` - RTK Query API slice with all customer endpoints

#### Providers
- `lib/providers/ReduxProvider.tsx` - Redux provider wrapper
- `lib/providers/NextAuthProvider.tsx` - NextAuth session provider

#### Authentication
- `lib/auth/nextauth.config.ts` - NextAuth configuration
- `app/api/auth/[...nextauth]/route.ts` - NextAuth API routes

#### Proxy API Routes
- `app/api/services/[...path]/route.ts` - Catch-all proxy routes with authentication

#### Authentication Pages
- `app/auth/signin/page.tsx` - Sign-in page

### 3. Updated Files

#### Layout
- `app/layout.tsx` - Added Redux and NextAuth providers

#### Components
- `components/Header.tsx` - Added authentication status and sign-in/out functionality

#### Pages
- `app/page.tsx` - Replaced direct API calls with RTK Query hooks

## Features

### RTK Query Benefits
- **Automatic Caching** - Reduces unnecessary API calls
- **Background Refetching** - Keeps data fresh
- **Loading States** - Built-in loading and error states
- **Optimistic Updates** - Better UX for mutations
- **Tag-based Invalidation** - Smart cache invalidation

### Authentication
- **NextAuth Integration** - Secure authentication with multiple providers
- **Automatic Headers** - Authentication headers added automatically
- **Protected Routes** - Some routes require authentication
- **Session Management** - Persistent sessions with JWT

### Proxy API Routes
- **Authentication Handling** - Automatic authentication header forwarding
- **Error Handling** - Proper error responses
- **Route Protection** - Some routes require authentication
- **Backend Abstraction** - Frontend doesn't need to know backend details

## Usage

### Using RTK Query Hooks

```typescript
import { useGetMenuItemsQuery } from '@/lib/store/api/customerApi'

function MenuComponent() {
  const {
    data: menuResponse,
    error,
    isLoading,
    refetch,
  } = useGetMenuItemsQuery({
    shopId: 'shop-id',
    filters: { is_available: true },
  })

  const menuItems = menuResponse?.data?.items || []
  
  // Handle loading, error, and success states
}
```

### Authentication

```typescript
import { useSession, signIn, signOut } from 'next-auth/react'

function AuthComponent() {
  const { data: session, status } = useSession()
  
  if (status === 'loading') return <p>Loading...</p>
  
  if (session) {
    return (
      <>
        <p>Signed in as {session.user.email}</p>
        <button onClick={() => signOut()}>Sign out</button>
      </>
    )
  }
  
  return (
    <>
      <p>Not signed in</p>
      <button onClick={() => signIn()}>Sign in</button>
    </>
  )
}
```

## Environment Variables

Copy `.env.example` to `.env.local` and configure:

```env
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here
NEXT_PUBLIC_CUSTOMER_API_URL=http://localhost:8081

# Optional OAuth providers
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
```

## API Routes

All API requests now go through proxy routes:

- **Public Routes**: `/api/services/shops/*`, `/api/services/menu/*`
- **Protected Routes**: `/api/services/auth/me`, `/api/services/orders/*`, `/api/services/reservations/*`

The proxy automatically:
1. Forwards requests to the customer backend
2. Adds authentication headers when user is signed in
3. Returns proper error responses
4. Handles CORS and other headers

## Benefits

1. **Better Performance** - RTK Query caching reduces API calls
2. **Better UX** - Loading states and error handling
3. **Security** - Authentication handled server-side
4. **Maintainability** - Centralized API logic
5. **Type Safety** - Full TypeScript support
6. **Scalability** - Easy to add new endpoints and features

## Next Steps

1. Add more authentication providers if needed
2. Implement user profile management
3. Add protected routes for orders and reservations
4. Implement real-time updates with WebSocket support
5. Add offline support with RTK Query persistence
