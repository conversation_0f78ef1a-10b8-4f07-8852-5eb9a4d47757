Running customer-api...
internal/types/common.go:6:2: missing go.sum entry for module providing package github.com/google/uuid (imported by customer-backend/internal/api/handlers); to add:
	go get customer-backend/internal/api/handlers
pkg/logger/logger.go:6:2: missing go.sum entry for module providing package github.com/sirupsen/logrus (imported by customer-backend/pkg/logger); to add:
	go get customer-backend/pkg/logger
internal/repositories/menu_repository.go:11:2: missing go.sum entry for module providing package gorm.io/gorm (imported by customer-backend/internal/api/routes); to add:
	go get customer-backend/internal/api/routes
internal/api/handlers/menu_handler.go:9:2: missing go.sum entry for module providing package github.com/gin-gonic/gin (imported by customer-backend/cmd/server); to add:
	go get customer-backend/cmd/server
internal/database/database.go:6:2: missing go.sum entry for module providing package gorm.io/driver/postgres (imported by customer-backend/internal/database); to add:
	go get customer-backend/internal/database
internal/database/database.go:8:2: missing go.sum entry for module providing package gorm.io/gorm/logger (imported by customer-backend/internal/database); to add:
	go get customer-backend/internal/database
cmd/server/main.go:19:2: missing go.sum entry for module providing package github.com/joho/godotenv (imported by customer-backend/cmd/server); to add:
	go get customer-backend/cmd/server
make[2]: *** [run] Error 1
